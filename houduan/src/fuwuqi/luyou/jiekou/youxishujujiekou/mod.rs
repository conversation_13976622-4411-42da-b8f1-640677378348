#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

mod jiekou_guaiwushuju;

use crate::fuwuqi::luyou::jiekou_dingyii;
use rocket::Route;

/// 获取游戏数据接口模块的所有路由
pub fn get_routes() -> Vec<Route> {
    let mut luyou = Vec::new();

    // 添加怪物数据查询接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_quanbu_xinxi::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_ziduan_xinxi::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_shanchu_guaiwu_huancun::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_suoyou_guaiwu_huancun::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_liebiao::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_liebiao_huancun::get_routes());

    // 添加怪物名字查询接口路由
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_leiming_jingque::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_jingque::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_guaiwu_mingzi_mohu::get_routes());
    luyou.extend(jiekou_guaiwushuju::jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_routes());

    luyou
}

/// 获取游戏数据接口模块的所有接口信息
pub fn get_jiekou_xinxi() -> Vec<(String, String, String, String)> {
    vec![
        jiekou_guaiwushuju::jiekou_guaiwu_quanbu_xinxi::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_ziduan_xinxi::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_shanchu_guaiwu_huancun::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_suoyou_guaiwu_huancun::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_liebiao::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_liebiao_huancun::get_jiekou_xinxi(),
        // 怪物名字查询接口信息
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_leiming_jingque::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_jingque::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_guaiwu_mingzi_mohu::get_jiekou_xinxi(),
        jiekou_guaiwushuju::jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_jiekou_xinxi(),
    ]
}