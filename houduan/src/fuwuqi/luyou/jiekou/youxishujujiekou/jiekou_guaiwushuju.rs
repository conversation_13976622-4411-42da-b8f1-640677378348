#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_mingzi_chaxun::{guaiwu_mingzi_chaxun_guanli, mingzi_chaxun_tiaojian};
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwujiegouti::guaiwu_ziduan_yingshe;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwushuju_liebiao_chaxun::guaiwu_liebiao_chaxun_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::guaiwushuju::guaiwushujuchuli::guaiwu_shuju_guanli;
use crate::chushihua::shujukuxitong::youxishujuchuli::wupinshuju::wupinjiegouti::fenye_canshu;
use crate::dingyii_jiekou;
use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{jiekou_dingyii, luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{delete, get, options, State};
use std::sync::Arc;

/// 辅助函数：创建怪物数据管理器（带Redis缓存支持）
fn chuangjian_guaiwu_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_shuju_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        let redis_kongzhi = guaiwu_redis_kongzhi::new(redis_state.as_ref().clone());
        guaiwu_shuju_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_kongzhi)
    } else {
        // 没有Redis，创建不带缓存的管理器
        guaiwu_shuju_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 查询怪物全部信息接口
#[get("/youxishuju/guaiwu/xinxi/<id>")]
pub async fn chaxun_guaiwu_quanbu_xinxi(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_quanbu_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_quanbu_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_quanbu_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 查询怪物全部信息
    match guaiwu_guanli.tongyong_chaxun(&id, guaiwu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功查询到怪物ID[{}]的全部信息", id),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功查询怪物ID[{}]", id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| format!("查询怪物ID[{}]失败", id))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("查询怪物ID[{}]时发生错误: {}", id, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/xinxi/<_id>")]
pub fn chaxun_guaiwu_quanbu_xinxi_yujian(_id: String) -> Status {
    Status::Ok
}

/// 查询怪物指定字段信息接口
#[get("/youxishuju/guaiwu/xinxi/<ziduan_ming>/<id>")]
pub async fn chaxun_guaiwu_ziduan_xinxi(
    ziduan_ming: String,
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_ziduan_xinxi::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_ziduan_xinxi::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_ziduan_xinxi::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证字段名是否有效
    if !guaiwu_ziduan_yingshe::jiancha_ziduan_youxiao(&ziduan_ming) {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            format!("无效的字段名: {}", ziduan_ming)
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 查询怪物指定字段信息
    match guaiwu_guanli.tongyong_chaxun(&id, &ziduan_ming).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功查询到怪物ID[{}]的字段[{}]信息", id, ziduan_ming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功查询怪物ID[{}]字段[{}]", id, ziduan_ming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| format!("查询怪物ID[{}]字段[{}]失败", id, ziduan_ming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("查询怪物ID[{}]字段[{}]时发生错误: {}", id, ziduan_ming, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/xinxi/<_ziduan_ming>/<_id>")]
pub fn chaxun_guaiwu_ziduan_xinxi_yujian(_ziduan_ming: String, _id: String) -> Status {
    Status::Ok
}

/// 删除怪物缓存接口
#[delete("/youxishuju/guaiwu/huancun/<id>")]
pub async fn shanchu_guaiwu_huancun(
    id: String,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_shanchu_guaiwu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_shanchu_guaiwu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_shanchu_guaiwu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 删除怪物缓存
    match guaiwu_guanli.shanchu_guaiwu_huancun(&id).await {
        Ok(shanchu_chenggong) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_chenggong {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    format!("成功删除怪物ID[{}]的缓存数据", id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功删除怪物ID[{}]缓存", id));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    format!("怪物ID[{}]的缓存不存在或Redis未启用", id),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("删除怪物ID[{}]缓存时发生错误: {}", id, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/huancun/<_id>")]
pub fn shanchu_guaiwu_huancun_yujian(_id: String) -> Status {
    Status::Ok
}

/// 清理所有怪物缓存接口
#[delete("/youxishuju/guaiwu/huancun")]
pub async fn qingchu_suoyou_guaiwu_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_suoyou_guaiwu_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_suoyou_guaiwu_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_suoyou_guaiwu_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物数据管理器（带Redis缓存）
    let guaiwu_guanli = chuangjian_guaiwu_guanli(mysql_guanli, redis_guanli);

    // 清理所有怪物缓存
    match guaiwu_guanli.qingchu_suoyou_guaiwu_huancun().await {
        Ok(shanchu_shuliang) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if shanchu_shuliang > 0 {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功清理所有怪物缓存，删除了{}个缓存项", shanchu_shuliang),
                    serde_json::json!({"shanchu_shuliang": shanchu_shuliang}),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功清理{}个怪物缓存", shanchu_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::chenggong_xiangying(
                    "没有找到需要清理的怪物缓存或Redis未启用".to_string(),
                    None,
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("清理所有怪物缓存时发生错误: {}", e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/huancun")]
pub fn qingchu_suoyou_guaiwu_huancun_yujian() -> Status {
    Status::Ok
}

/// 查询怪物列表接口（分页）
#[get("/youxishuju/guaiwu/liebiao?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_liebiao(
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_liebiao::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_liebiao::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_liebiao::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 设置默认分页参数
    let yema = yema.unwrap_or(1);
    let meiye_shuliang = meiye_shuliang.unwrap_or(10);

    // 验证分页参数
    if yema == 0 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "页码不能为0，请使用从1开始的页码".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    if meiye_shuliang == 0 || meiye_shuliang > 100 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "每页数量必须在1-100之间".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema, meiye_shuliang);

    // 创建怪物列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        guaiwu_liebiao_chaxun_guanli::new_with_redis(
            mysql_guanli.as_ref().clone(),
            redis_state.as_ref().clone(),
        )
    } else {
        guaiwu_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 查询怪物列表
    match liebiao_guanli.huoqu_guaiwu_liebiao(&fenye_canshu).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功查询到第{}页怪物列表，共{}条记录", yema, jieguo.guaiwu_liebiao.len()),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功查询第{}页怪物列表，每页{}条", yema, meiye_shuliang));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| "查询怪物列表失败".to_string())
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("查询怪物列表时发生错误: {}", e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/liebiao")]
pub fn chaxun_guaiwu_liebiao_yujian() -> Status {
    Status::Ok
}

/// 清除怪物列表缓存接口
#[delete("/youxishuju/guaiwu/liebiao/huancun")]
pub async fn qingchu_guaiwu_liebiao_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_liebiao_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_liebiao_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_liebiao_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物列表查询管理器
    let liebiao_guanli = if let Some(redis_state) = redis_guanli {
        guaiwu_liebiao_chaxun_guanli::new_with_redis(
            mysql_guanli.as_ref().clone(),
            redis_state.as_ref().clone(),
        )
    } else {
        guaiwu_liebiao_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    };

    // 清除怪物列表缓存
    match liebiao_guanli.qingchu_liebiao_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying(
                "成功清除怪物列表缓存".to_string(),
                None,
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, "成功清除怪物列表缓存");
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("清除怪物列表缓存时发生错误: {}", e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/liebiao/huancun")]
pub fn qingchu_guaiwu_liebiao_huancun_yujian() -> Status {
    Status::Ok
}

// 使用宏定义查询全部信息接口
dingyii_jiekou!(
    jiekou_guaiwu_quanbu_xinxi,
    lujing: "/youxishuju/guaiwu/xinxi/{id}",
    fangfa: "GET",
    miaoshu: "查询怪物全部信息",
    jieshao: "根据怪物ID查询怪物的全部信息，包括基础信息和汇总信息",
    routes: [chaxun_guaiwu_quanbu_xinxi, chaxun_guaiwu_quanbu_xinxi_yujian]
);

// 使用宏定义查询字段信息接口
dingyii_jiekou!(
    jiekou_guaiwu_ziduan_xinxi,
    lujing: "/youxishuju/guaiwu/xinxi/{字段名字}/{id}",
    fangfa: "GET",
    miaoshu: "查询怪物指定字段信息",
    jieshao: "根据字段名和怪物ID查询怪物的指定字段信息",
    routes: [chaxun_guaiwu_ziduan_xinxi, chaxun_guaiwu_ziduan_xinxi_yujian]
);

// 使用宏定义删除缓存接口
dingyii_jiekou!(
    jiekou_shanchu_guaiwu_huancun,
    lujing: "/youxishuju/guaiwu/huancun/{id}",
    fangfa: "DELETE",
    miaoshu: "删除怪物缓存",
    jieshao: "删除指定怪物ID的Redis缓存数据，不影响其他数据",
    routes: [shanchu_guaiwu_huancun, shanchu_guaiwu_huancun_yujian]
);

// 使用宏定义清理所有缓存接口
dingyii_jiekou!(
    jiekou_qingchu_suoyou_guaiwu_huancun,
    lujing: "/youxishuju/guaiwu/huancun",
    fangfa: "DELETE",
    miaoshu: "清理所有怪物缓存",
    jieshao: "一键清理所有怪物全部信息的Redis缓存，不影响其他类型的缓存",
    routes: [qingchu_suoyou_guaiwu_huancun, qingchu_suoyou_guaiwu_huancun_yujian]
);

// 使用宏定义怪物列表查询接口
dingyii_jiekou!(
    jiekou_guaiwu_liebiao,
    lujing: "/youxishuju/guaiwu/liebiao?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "查询怪物列表",
    jieshao: "分页查询怪物列表，支持Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_liebiao, chaxun_guaiwu_liebiao_yujian]
);

// 使用宏定义清除怪物列表缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_liebiao_huancun,
    lujing: "/youxishuju/guaiwu/liebiao/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物列表缓存",
    jieshao: "清除所有怪物列表的Redis缓存，不影响怪物详情缓存",
    routes: [qingchu_guaiwu_liebiao_huancun, qingchu_guaiwu_liebiao_huancun_yujian]
);

/// 辅助函数：创建怪物名字查询管理器（带Redis缓存支持）
fn chuangjian_guaiwu_mingzi_chaxun_guanli(
    mysql_guanli: &Arc<mysql_lianjie_guanli>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> guaiwu_mingzi_chaxun_guanli {
    if let Some(redis_state) = redis_guanli {
        // 有Redis，创建带缓存的管理器
        guaiwu_mingzi_chaxun_guanli::new_with_redis(mysql_guanli.as_ref().clone(), redis_state.as_ref().clone())
    } else {
        // 没有Redis，创建不带缓存的管理器
        guaiwu_mingzi_chaxun_guanli::new(mysql_guanli.as_ref().clone())
    }
}

/// 根据类名精确查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/leiming/<leiming>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_leiming_jingque(
    leiming: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_leiming_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_leiming_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_leiming_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::leiming_jingque(leiming.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功查询到类名为[{}]的怪物列表", leiming),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功查询类名[{}]的怪物", leiming));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| format!("查询类名[{}]的怪物失败", leiming))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("查询类名[{}]的怪物时发生错误: {}", leiming, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/leiming/<_leiming>")]
pub fn chaxun_guaiwu_by_leiming_jingque_yujian(_leiming: String) -> Status {
    Status::Ok
}

/// 根据名字精确查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/jingque/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_mingzi_jingque(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_jingque::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_jingque::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_jingque::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_jingque(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功查询到名字为[{}]的怪物列表", mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功查询名字[{}]的怪物", mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| format!("查询名字[{}]的怪物失败", mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("查询名字[{}]的怪物时发生错误: {}", mingzi, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/jingque/<_mingzi>")]
pub fn chaxun_guaiwu_by_mingzi_jingque_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 根据名字模糊查询怪物列表接口
#[get("/youxishuju/guaiwu/mingzi/mohu/<mingzi>?<yema>&<meiye_shuliang>")]
pub async fn chaxun_guaiwu_by_mingzi_mohu(
    mingzi: String,
    yema: Option<u32>,
    meiye_shuliang: Option<u32>,
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_guaiwu_mingzi_mohu::get_miaoshu();
    let qingqiu_lujing = jiekou_guaiwu_mingzi_mohu::get_lujing();
    let qingqiu_fangfa = jiekou_guaiwu_mingzi_mohu::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 验证查询字符串长度，模糊查询至少需要2个字符
    if mingzi.chars().count() < 2 {
        let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
        let xiangying = mingwen_xiangying::shibai_xiangying(
            "模糊查询至少需要输入2个字符".to_string()
        );
        luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 400);
        return Json(xiangying);
    }

    // 创建分页参数
    let fenye_canshu = fenye_canshu::new(yema.unwrap_or(1), meiye_shuliang.unwrap_or(20));

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 创建查询条件
    let chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(mingzi.clone(), fenye_canshu);

    // 执行查询
    match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&chaxun_tiaojian).await {
        Ok(jieguo) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;

            if jieguo.chenggong {
                let xiangying = mingwen_xiangying::chenggong_with_shuju(
                    format!("成功模糊查询到包含[{}]的怪物列表", mingzi),
                    serde_json::to_value(&jieguo).unwrap_or_default(),
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
                luyou_rizhi_xinxi(jiekou_ming, &format!("成功模糊查询包含[{}]的怪物", mingzi));
                Json(xiangying)
            } else {
                let xiangying = mingwen_xiangying::shibai_xiangying(
                    jieguo.cuowu_xinxi.unwrap_or_else(|| format!("模糊查询包含[{}]的怪物失败", mingzi))
                );
                luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 404);
                Json(xiangying)
            }
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("模糊查询包含[{}]的怪物时发生错误: {}", mingzi, e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/mohu/<_mingzi>")]
pub fn chaxun_guaiwu_by_mingzi_mohu_yujian(_mingzi: String) -> Status {
    Status::Ok
}

/// 清除怪物名字查询缓存接口
#[delete("/youxishuju/guaiwu/mingzi/huancun")]
pub async fn qingchu_guaiwu_mingzi_chaxun_huancun(
    mysql_guanli: &State<Arc<mysql_lianjie_guanli>>,
    redis_guanli: Option<&State<Arc<redis_lianjie_guanli>>>,
) -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_miaoshu();
    let qingqiu_lujing = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_lujing();
    let qingqiu_fangfa = jiekou_qingchu_guaiwu_mingzi_chaxun_huancun::get_fangfa();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();

    // 创建怪物名字查询管理器
    let mingzi_chaxun_guanli = chuangjian_guaiwu_mingzi_chaxun_guanli(mysql_guanli, redis_guanli);

    // 清除缓存
    match mingzi_chaxun_guanli.qingchu_mingzi_chaxun_huancun().await {
        Ok(_) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::chenggong_xiangying("成功清除怪物名字查询缓存".to_string(), None);
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
            luyou_rizhi_xinxi(jiekou_ming, "成功清除怪物名字查询缓存");
            Json(xiangying)
        }
        Err(e) => {
            let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
            let xiangying = mingwen_xiangying::shibai_xiangying(
                format!("清除怪物名字查询缓存时发生错误: {}", e)
            );
            luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 500);
            Json(xiangying)
        }
    }
}

#[options("/youxishuju/guaiwu/mingzi/huancun")]
pub fn qingchu_guaiwu_mingzi_chaxun_huancun_yujian() -> Status {
    Status::Ok
}

// 使用宏定义怪物类名精确查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_leiming_jingque,
    lujing: "/youxishuju/guaiwu/mingzi/leiming/{类名}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据类名精确查询怪物列表",
    jieshao: "根据怪物类名精确查询怪物列表，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_leiming_jingque, chaxun_guaiwu_by_leiming_jingque_yujian]
);

// 使用宏定义怪物名字精确查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_jingque,
    lujing: "/youxishuju/guaiwu/mingzi/jingque/{名字}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字精确查询怪物列表",
    jieshao: "根据怪物名字精确查询怪物列表，支持分页和Redis缓存。页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_mingzi_jingque, chaxun_guaiwu_by_mingzi_jingque_yujian]
);

// 使用宏定义怪物名字模糊查询接口
dingyii_jiekou!(
    jiekou_guaiwu_mingzi_mohu,
    lujing: "/youxishuju/guaiwu/mingzi/mohu/{名字关键词}?yema={页码}&meiye_shuliang={每页数量}",
    fangfa: "GET",
    miaoshu: "根据名字模糊查询怪物列表",
    jieshao: "根据怪物名字关键词模糊查询怪物列表，支持分页和Redis缓存。关键词至少需要2个字符，页码从1开始，每页数量1-100",
    routes: [chaxun_guaiwu_by_mingzi_mohu, chaxun_guaiwu_by_mingzi_mohu_yujian]
);

// 使用宏定义清除怪物名字查询缓存接口
dingyii_jiekou!(
    jiekou_qingchu_guaiwu_mingzi_chaxun_huancun,
    lujing: "/youxishuju/guaiwu/mingzi/huancun",
    fangfa: "DELETE",
    miaoshu: "清除怪物名字查询缓存",
    jieshao: "清除所有怪物名字查询的Redis缓存，包括类名查询、精确名字查询和模糊名字查询的缓存",
    routes: [qingchu_guaiwu_mingzi_chaxun_huancun, qingchu_guaiwu_mingzi_chaxun_huancun_yujian]
);